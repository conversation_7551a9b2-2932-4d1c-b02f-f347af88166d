import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface IpAnonymizationCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class IpAnonymizationCheck {
  /**
   * Check for IP anonymization in analytics scripts
   * REAL ANALYSIS - analyzes actual JavaScript code
   */
  async performCheck(config: IpAnonymizationCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze JavaScript for analytics configurations
      const analyticsAnalysis = await this.analyzeAnalyticsScripts(page);
      
      // Check Google Analytics specifically
      const gaAnalysis = await this.checkGoogleAnalytics(page);
      
      // Calculate score based on findings
      let totalAnalytics = analyticsAnalysis.analyticsFound.length;
      let anonymizedAnalytics = 0;

      // Google Analytics check
      if (gaAnalysis.found) {
        totalAnalytics++;
        if (gaAnalysis.anonymized) {
          anonymizedAnalytics++;
          evidence.push({
            type: 'text',
            description: 'Google Analytics IP anonymization enabled',
            value: gaAnalysis.evidence,
          });
        } else {
          evidence.push({
            type: 'text',
            description: 'Google Analytics found without IP anonymization',
            value: 'anonymizeIp not detected',
          });
        }
      }

      // Other analytics platforms
      for (const analytics of analyticsAnalysis.analyticsFound) {
        if (analytics.hasAnonymization) {
          anonymizedAnalytics++;
          evidence.push({
            type: 'text',
            description: `${analytics.platform} has IP anonymization`,
            value: analytics.evidence,
          });
        } else {
          evidence.push({
            type: 'text',
            description: `${analytics.platform} lacks IP anonymization`,
            value: 'No anonymization detected',
          });
        }
      }

      // Calculate score
      if (totalAnalytics === 0) {
        score = 100; // No analytics = no IP collection issue
        evidence.push({
          type: 'text',
          description: 'No analytics platforms detected',
          value: 'No IP collection concerns',
        });
      } else {
        score = Math.round((anonymizedAnalytics / totalAnalytics) * 100);
      }

      const passed = score >= 80;

      return {
        ruleId: 'GDPR-011',
        ruleName: 'IP Address as Personal Data',
        category: 'data_protection',
        passed,
        score,
        weight: 5,
        severity: 'medium',
        evidence,
        recommendations: this.generateIpRecommendations(totalAnalytics, anonymizedAnalytics),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-011',
        ruleName: 'IP Address as Personal Data',
        category: 'data_protection',
        passed: false,
        score: 0,
        weight: 5,
        severity: 'medium',
        evidence: [{
          type: 'text',
          description: 'IP anonymization check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement IP anonymization',
          description: 'Enable IP anonymization in all analytics platforms',
          implementation: 'Configure analytics tools to anonymize IP addresses',
          effort: 'minimal',
          impact: 'medium',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Check Google Analytics configuration
   */
  private async checkGoogleAnalytics(page: Page): Promise<{
    found: boolean;
    anonymized: boolean;
    evidence: string;
  }> {
    return await page.evaluate(() => {
      // Check for Google Analytics
      const scripts = Array.from(document.querySelectorAll('script'));
      let found = false;
      let anonymized = false;
      let evidence = '';

      // Check for gtag configuration
      for (const script of scripts) {
        const content = script.textContent || '';
        
        // Look for Google Analytics
        if (content.includes('gtag') || content.includes('ga(') || content.includes('googletagmanager')) {
          found = true;
          
          // Check for IP anonymization
          if (content.includes('anonymize_ip') || content.includes('anonymizeIp')) {
            anonymized = true;
            evidence = 'anonymize_ip configuration found';
          } else {
            evidence = 'Google Analytics found without anonymize_ip';
          }
          break;
        }
      }

      // Check for global gtag function
      if (!found && typeof (window as any).gtag === 'function') {
        found = true;
        evidence = 'gtag function detected';
      }

      // Check for ga function
      if (!found && typeof (window as any).ga === 'function') {
        found = true;
        evidence = 'ga function detected';
      }

      return { found, anonymized, evidence };
    });
  }

  /**
   * Analyze other analytics scripts
   */
  private async analyzeAnalyticsScripts(page: Page): Promise<{
    analyticsFound: Array<{
      platform: string;
      hasAnonymization: boolean;
      evidence: string;
    }>;
  }> {
    return await page.evaluate(() => {
      const analyticsFound: Array<{
        platform: string;
        hasAnonymization: boolean;
        evidence: string;
      }> = [];

      const scripts = Array.from(document.querySelectorAll('script'));
      
      const analyticsPlatforms = [
        {
          name: 'Adobe Analytics',
          patterns: ['adobe', 'omniture', 's_code'],
          anonymizationPatterns: ['visitorAPI', 'anonymize'],
        },
        {
          name: 'Hotjar',
          patterns: ['hotjar'],
          anonymizationPatterns: ['anonymizeIP', 'disable'],
        },
        {
          name: 'Mixpanel',
          patterns: ['mixpanel'],
          anonymizationPatterns: ['ip', 'anonymize'],
        },
      ];

      for (const script of scripts) {
        const content = script.textContent?.toLowerCase() || '';
        const src = script.src?.toLowerCase() || '';
        const fullContent = content + ' ' + src;

        for (const platform of analyticsPlatforms) {
          const platformFound = platform.patterns.some(pattern => 
            fullContent.includes(pattern)
          );

          if (platformFound) {
            const hasAnonymization = platform.anonymizationPatterns.some(pattern =>
              fullContent.includes(pattern)
            );

            analyticsFound.push({
              platform: platform.name,
              hasAnonymization,
              evidence: hasAnonymization 
                ? 'Anonymization configuration detected'
                : 'No anonymization detected',
            });
          }
        }
      }

      return { analyticsFound };
    });
  }

  /**
   * Generate IP anonymization recommendations
   */
  private generateIpRecommendations(total: number, anonymized: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (total > anonymized) {
      recommendations.push({
        priority: 1,
        title: 'Enable IP anonymization',
        description: `${total - anonymized} analytics platform(s) need IP anonymization`,
        implementation: 'Configure analytics tools to anonymize IP addresses before processing',
        effort: 'minimal',
        impact: 'medium',
      });
    }

    if (total > 0) {
      recommendations.push({
        priority: 2,
        title: 'Document IP processing',
        description: 'Update privacy policy to explain IP address processing and anonymization',
        implementation: 'Add section about IP address handling in privacy policy',
        effort: 'minimal',
        impact: 'low',
      });
    }

    return recommendations;
  }
}
