/**
 * GDPR Compliance Constants
 * 
 * This file contains all constants used throughout the GDPR compliance module,
 * including rule definitions, weights, patterns, and configuration values.
 */

import { GdprRuleId, GdprCategory, Severity } from './types';

/**
 * GDPR Rule Definitions with weights and metadata
 */
export interface GdprRuleDefinition {
  id: GdprRuleId;
  name: string;
  description: string;
  category: GdprCategory;
  weight: number;
  severity: Severity;
  automated: boolean;
  article: string; // GDPR Article reference
}

/**
 * Complete GDPR Rules Configuration
 */
export const GDPR_RULES: Record<GdprRuleId, GdprRuleDefinition> = {
  'GDPR-001': {
    id: 'GDPR-001',
    name: 'HTTPS/TLS Encryption',
    description: 'Website must use HTTPS encryption for data protection',
    category: 'security',
    weight: 0.8,
    severity: 'critical',
    automated: true,
    article: 'Article 32'
  },
  'GDPR-002': {
    id: 'GDPR-002',
    name: 'Privacy Policy Presence',
    description: 'Website must have an accessible privacy policy',
    category: 'privacy_policy',
    weight: 0.9,
    severity: 'critical',
    automated: true,
    article: 'Article 13'
  },
  'GDPR-003': {
    id: 'GDPR-003',
    name: 'Privacy Notice Content',
    description: 'Privacy policy must contain required GDPR information',
    category: 'privacy_policy',
    weight: 0.85,
    severity: 'high',
    automated: false,
    article: 'Article 13'
  },
  'GDPR-004': {
    id: 'GDPR-004',
    name: 'Cookie Consent Banner',
    description: 'Website must display cookie consent mechanism',
    category: 'consent',
    weight: 0.9,
    severity: 'critical',
    automated: true,
    article: 'Article 7'
  },
  'GDPR-005': {
    id: 'GDPR-005',
    name: 'Cookie Classification',
    description: 'Cookies must be properly classified and documented',
    category: 'cookies',
    weight: 0.7,
    severity: 'medium',
    automated: true,
    article: 'Article 7'
  },
  'GDPR-006': {
    id: 'GDPR-006',
    name: 'Third-party Tracker Detection',
    description: 'Third-party trackers must have proper consent mechanisms',
    category: 'cookies',
    weight: 0.8,
    severity: 'high',
    automated: true,
    article: 'Article 7'
  },
  'GDPR-007': {
    id: 'GDPR-007',
    name: 'Cookie Security Attributes',
    description: 'Cookies must have appropriate security attributes',
    category: 'security',
    weight: 0.6,
    severity: 'medium',
    automated: true,
    article: 'Article 32'
  },
  'GDPR-008': {
    id: 'GDPR-008',
    name: 'Global Privacy Control (GPC)',
    description: 'Website should respect Global Privacy Control signals',
    category: 'consent',
    weight: 0.5,
    severity: 'low',
    automated: true,
    article: 'Article 7'
  },
  'GDPR-009': {
    id: 'GDPR-009',
    name: 'Do Not Track (DNT)',
    description: 'Website should respect Do Not Track headers',
    category: 'consent',
    weight: 0.4,
    severity: 'low',
    automated: true,
    article: 'Article 7'
  },
  'GDPR-010': {
    id: 'GDPR-010',
    name: 'Form Consent Controls',
    description: 'Forms must have proper consent mechanisms',
    category: 'consent',
    weight: 0.75,
    severity: 'medium',
    automated: false,
    article: 'Article 7'
  },
  'GDPR-011': {
    id: 'GDPR-011',
    name: 'Security Headers',
    description: 'Website must implement proper security headers',
    category: 'security',
    weight: 0.6,
    severity: 'medium',
    automated: true,
    article: 'Article 32'
  },
  'GDPR-012': {
    id: 'GDPR-012',
    name: 'IP Address Anonymization',
    description: 'Analytics tools must anonymize IP addresses',
    category: 'data_protection',
    weight: 0.7,
    severity: 'medium',
    automated: true,
    article: 'Article 25'
  },
  'GDPR-013': {
    id: 'GDPR-013',
    name: 'Data Subject Rights',
    description: 'Website must provide mechanisms for data subject rights',
    category: 'data_rights',
    weight: 0.85,
    severity: 'high',
    automated: false,
    article: 'Articles 15-22'
  },
  'GDPR-014': {
    id: 'GDPR-014',
    name: 'Special Category Data Protection',
    description: 'Special category data must have enhanced protection',
    category: 'data_protection',
    weight: 0.9,
    severity: 'critical',
    automated: false,
    article: 'Article 9'
  },
  'GDPR-015': {
    id: 'GDPR-015',
    name: 'Children\'s Consent Verification',
    description: 'Websites targeting children must verify parental consent',
    category: 'consent',
    weight: 0.95,
    severity: 'critical',
    automated: false,
    article: 'Article 8'
  },
  'GDPR-016': {
    id: 'GDPR-016',
    name: 'DPO Contact Information',
    description: 'Data Protection Officer contact must be available',
    category: 'organizational',
    weight: 0.6,
    severity: 'medium',
    automated: true,
    article: 'Article 37'
  },
  'GDPR-017': {
    id: 'GDPR-017',
    name: 'EU Representative Information',
    description: 'Non-EU companies must provide EU representative details',
    category: 'organizational',
    weight: 0.7,
    severity: 'medium',
    automated: true,
    article: 'Article 27'
  },
  'GDPR-018': {
    id: 'GDPR-018',
    name: 'International Data Transfers',
    description: 'International transfers must have adequate safeguards',
    category: 'data_protection',
    weight: 0.8,
    severity: 'high',
    automated: false,
    article: 'Articles 44-49'
  },
  'GDPR-019': {
    id: 'GDPR-019',
    name: 'Data Breach Notification',
    description: 'Website must have breach notification procedures',
    category: 'organizational',
    weight: 0.75,
    severity: 'high',
    automated: true,
    article: 'Article 33'
  },
  'GDPR-020': {
    id: 'GDPR-020',
    name: 'Data Protection Impact Assessment',
    description: 'High-risk processing must have DPIA documentation',
    category: 'organizational',
    weight: 0.7,
    severity: 'medium',
    automated: false,
    article: 'Article 35'
  },
  'GDPR-021': {
    id: 'GDPR-021',
    name: 'Data Retention Policies',
    description: 'Website must have clear data retention policies',
    category: 'data_protection',
    weight: 0.75,
    severity: 'medium',
    automated: true,
    article: 'Article 5'
  }
};

/**
 * Category Weights for Overall Score Calculation
 */
export const CATEGORY_WEIGHTS: Record<GdprCategory, number> = {
  security: 0.20,
  privacy_policy: 0.18,
  consent: 0.25,
  cookies: 0.15,
  data_rights: 0.12,
  data_protection: 0.06,
  data_sharing: 0.02,
  organizational: 0.02,
};

/**
 * Risk Level Thresholds
 */
export const RISK_THRESHOLDS = {
  CRITICAL: 60,
  HIGH: 75,
  MEDIUM: 85,
  LOW: 100
} as const;

/**
 * Scan Configuration Defaults
 */
export const DEFAULT_SCAN_CONFIG = {
  timeout: 300000, // 5 minutes
  maxPages: 10,
  enableCookieAnalysis: true,
  enableTrackerDetection: true,
  enableConsentTesting: true,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
} as const;

/**
 * Common Cookie Categories
 */
export const COOKIE_CATEGORIES = {
  ESSENTIAL: 'essential',
  ANALYTICS: 'analytics',
  MARKETING: 'marketing',
  FUNCTIONAL: 'functional'
} as const;

/**
 * Tracker Categories
 */
export const TRACKER_CATEGORIES = {
  ANALYTICS: 'analytics',
  ADVERTISING: 'advertising',
  SOCIAL: 'social',
  FUNCTIONAL: 'functional',
  UNKNOWN: 'unknown'
} as const;

/**
 * GDPR Scoring Weights for risk-weighted scoring algorithm
 */
export const GDPR_SCORING_WEIGHTS: Record<GdprRuleId, number> = {
  // Critical Requirements (High Weight)
  'GDPR-001': 8, // HTTPS/TLS
  'GDPR-002': 7, // Privacy Policy Presence
  'GDPR-004': 9, // Cookie Consent
  'GDPR-005': 8, // Cookie Classification

  // High Priority (Medium-High Weight)
  'GDPR-003': 7, // Privacy Notice Content
  'GDPR-006': 6, // Tracker Detection
  'GDPR-012': 7, // Data Subject Rights

  // Medium Priority (Medium Weight)
  'GDPR-007': 5, // Cookie Attributes
  'GDPR-008': 4, // GPC/DNT
  'GDPR-009': 6, // Form Consent
  'GDPR-010': 5, // Security Headers
  'GDPR-011': 5, // IP Anonymization

  // Standard Requirements (Lower Weight)
  'GDPR-013': 4, // Special Category Data
  'GDPR-015': 3, // DPO Contact
  'GDPR-016': 5, // Data Transfers
  'GDPR-017': 3, // Breach Notification
  'GDPR-019': 4, // Data Retention
  'GDPR-020': 3, // Processor Agreements
  'GDPR-021': 2, // Imprint Contact

  // Manual Review Items (Flagged)
  'GDPR-014': 0, // Children's Consent
  'GDPR-018': 0, // DPIA
} as const;

/**
 * GDPR Scan Configuration
 */
export const GDPR_SCAN_CONFIG = {
  DEFAULT_TIMEOUT: 300000, // 5 minutes
  MAX_PAGES: 50,
  DEFAULT_USER_AGENT: 'GDPR-Compliance-Scanner/1.0',
  COOKIE_ANALYSIS_TIMEOUT: 30000,
  CONSENT_INTERACTION_TIMEOUT: 15000,
} as const;
