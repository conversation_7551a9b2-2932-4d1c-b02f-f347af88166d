/**
 * GDPR Part 3C Implementation Test
 * 
 * Test to verify all 21 GDPR checks are properly implemented and can be executed.
 */

import { GdprOrchestrator } from '../orchestrator';
import { GdprScanRequest } from '../types';

describe('GDPR Part 3C - Complete Implementation', () => {
  let orchestrator: GdprOrchestrator;

  beforeEach(() => {
    orchestrator = new GdprOrchestrator();
  });

  test('should have all 21 GDPR checks implemented', async () => {
    // Import all checks to verify they exist
    const checks = await import('../checks');

    // Verify key checks are exported
    expect(checks.HttpsTlsCheck).toBeDefined();
    expect(checks.PrivacyPolicyCheck).toBeDefined();
    expect(checks.CookieConsentCheck).toBeDefined();
    expect(checks.SecurityHeadersCheck).toBeDefined();
    expect(checks.DataRightsCheck).toBeDefined();
    expect(checks.CookieAttributesCheck).toBeDefined();
    expect(checks.GpcDntCheck).toBeDefined();
    expect(checks.FormConsentCheck).toBeDefined();
    expect(checks.SpecialDataCheck).toBeDefined();
    expect(checks.ChildrenConsentCheck).toBeDefined();
    expect(checks.DpoContactCheck).toBeDefined();
    expect(checks.DataTransfersCheck).toBeDefined();
    expect(checks.BreachNotificationCheck).toBeDefined();
    expect(checks.DpiaCheck).toBeDefined();
    expect(checks.DataRetentionCheck).toBeDefined();
    expect(checks.ProcessorAgreementsCheck).toBeDefined();
    expect(checks.ImprintContactCheck).toBeDefined();
  });

  test('should execute all checks without errors', async () => {
    const scanRequest: GdprScanRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        timeout: 10000, // Short timeout for testing
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'Test-Agent/1.0',
      },
    };

    // This should not throw an error
    const result = await orchestrator.performComprehensiveScan('test-user-id', scanRequest);
    
    expect(result).toBeDefined();
    expect(result.scanId).toBeDefined();
    expect(result.targetUrl).toBe(scanRequest.targetUrl);
    expect(result.checks).toBeDefined();
    expect(Array.isArray(result.checks)).toBe(true);
    expect(result.checks.length).toBe(21); // All 21 GDPR checks
    
    // Verify all rule IDs are present
    const ruleIds = result.checks.map(check => check.ruleId).sort();
    const expectedRuleIds = [
      'GDPR-001', 'GDPR-002', 'GDPR-003', 'GDPR-004', 'GDPR-005',
      'GDPR-006', 'GDPR-007', 'GDPR-008', 'GDPR-009', 'GDPR-010',
      'GDPR-011', 'GDPR-012', 'GDPR-013', 'GDPR-014', 'GDPR-015',
      'GDPR-016', 'GDPR-017', 'GDPR-018', 'GDPR-019', 'GDPR-020',
      'GDPR-021'
    ];
    
    expect(ruleIds).toEqual(expectedRuleIds);
  }, 30000); // 30 second timeout for full scan

  test('should handle manual review checks correctly', async () => {
    const scanRequest: GdprScanRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        timeout: 10000,
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'Test-Agent/1.0',
      },
    };

    const result = await orchestrator.performComprehensiveScan('test-user-id', scanRequest);
    
    // Find manual review checks
    const manualReviewChecks = result.checks.filter(check => check.manualReviewRequired);
    
    // Should have at least the known manual review checks
    const expectedManualReviewRules = ['GDPR-013', 'GDPR-014', 'GDPR-016', 'GDPR-018'];
    
    for (const ruleId of expectedManualReviewRules) {
      const check = result.checks.find(c => c.ruleId === ruleId);
      expect(check).toBeDefined();
      expect(check?.manualReviewRequired).toBe(true);
    }
    
    expect(manualReviewChecks.length).toBeGreaterThan(0);
  }, 30000);
});
