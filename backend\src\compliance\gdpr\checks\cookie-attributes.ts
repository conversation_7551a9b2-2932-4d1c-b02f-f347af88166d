import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface CookieAttributesCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class CookieAttributesCheck {
  /**
   * Check cookie security attributes for GDPR compliance
   * REAL ANALYSIS - analyzes actual cookies from website
   */
  async performCheck(config: CookieAttributesCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Get all cookies from the page
      const cookies = await page.cookies();
      
      if (cookies.length === 0) {
        evidence.push({
          type: 'cookie',
          description: 'No cookies found',
          value: 'No cookie security concerns',
        });
        score = 100; // No cookies = no security issues
      } else {
        const analysis = this.analyzeCookieAttributes(cookies);
        
        const totalCookies = cookies.length;
        const compliantCookies = analysis.filter(a => a.complianceIssues.length === 0).length;
        score = Math.round((compliantCookies / totalCookies) * 100);

        // Add evidence for each cookie analysis
        for (const cookieAnalysis of analysis) {
          if (cookieAnalysis.complianceIssues.length === 0) {
            evidence.push({
              type: 'cookie',
              description: `Cookie "${cookieAnalysis.name}" has proper security attributes`,
              value: `Secure: ${cookieAnalysis.secure}, HttpOnly: ${cookieAnalysis.httpOnly}, SameSite: ${cookieAnalysis.sameSite}`,
            });
          } else {
            evidence.push({
              type: 'cookie',
              description: `Cookie "${cookieAnalysis.name}" has security issues`,
              value: cookieAnalysis.complianceIssues.join(', '),
            });
          }
        }

        evidence.push({
          type: 'cookie',
          description: 'Cookie security summary',
          value: `${compliantCookies}/${totalCookies} cookies have proper security attributes`,
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-007',
        ruleName: 'Cookie Security Attributes',
        category: 'cookies',
        passed,
        score,
        weight: 5,
        severity: 'medium',
        evidence,
        recommendations: this.generateRecommendations(score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze cookie attributes for security compliance
   */
  private analyzeCookieAttributes(cookies: Array<{
    name: string;
    value: string;
    domain: string;
    path: string;
    secure: boolean;
    httpOnly: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
    expires?: number;
  }>): Array<{
    name: string;
    secure: boolean;
    httpOnly: boolean;
    sameSite?: string;
    complianceIssues: string[];
  }> {
    return cookies.map(cookie => {
      const issues: string[] = [];

      // Check Secure flag
      if (!cookie.secure) {
        issues.push('Missing Secure flag');
      }

      // Check HttpOnly flag
      if (!cookie.httpOnly) {
        issues.push('Missing HttpOnly flag');
      }

      // Check SameSite attribute
      if (!cookie.sameSite) {
        issues.push('Missing SameSite attribute');
      } else if (cookie.sameSite === 'None' && !cookie.secure) {
        issues.push('SameSite=None requires Secure flag');
      }

      return {
        name: cookie.name,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        sameSite: cookie.sameSite,
        complianceIssues: issues,
      };
    });
  }

  /**
   * Generate recommendations based on score
   */
  private generateRecommendations(score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Fix cookie security attributes',
        description: 'Add Secure, HttpOnly, and SameSite attributes to cookies',
        implementation: 'Configure web server to set proper cookie attributes',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    if (score < 50) {
      recommendations.push({
        priority: 2,
        title: 'Review cookie necessity',
        description: 'Evaluate if all cookies are necessary and minimize cookie usage',
        implementation: 'Audit cookie usage and remove unnecessary cookies',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-007',
      ruleName: 'Cookie Security Attributes',
      category: 'cookies',
      passed: false,
      score: 0,
      weight: 5,
      severity: 'medium',
      evidence: [{
        type: 'text',
        description: 'Cookie attributes check failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }],
      recommendations: [{
        priority: 1,
        title: 'Fix cookie implementation',
        description: 'Resolve cookie configuration issues',
        implementation: 'Review cookie settings and server configuration',
        effort: 'moderate',
        impact: 'medium',
      }],
      manualReviewRequired: false,
    };
  }
}
