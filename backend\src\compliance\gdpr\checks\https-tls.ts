import * as https from 'https';
import { URL } from 'url';
import { TLSSocket } from 'tls';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface HttpsTlsCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class HttpsTlsCheck {
  /**
   * Check HTTPS/TLS encryption compliance
   * REAL ANALYSIS - connects to actual website
   */
  async performCheck(config: HttpsTlsCheckConfig): Promise<GdprCheckResult> {
    const evidence: Evidence[] = [];
    let passed = true;
    const issues: string[] = [];

    try {
      const url = new URL(config.targetUrl);
      
      // Check if URL uses HTTPS
      if (url.protocol !== 'https:') {
        passed = false;
        issues.push('Website does not use HTTPS encryption');
        evidence.push({
          type: 'text',
          description: 'Protocol check failed',
          value: url.protocol,
        });
      }

      // Perform actual TLS certificate analysis
      const tlsInfo = await this.analyzeTlsCertificate(url.hostname, url.port || '443');
      
      if (!tlsInfo.valid) {
        passed = false;
        issues.push('Invalid or expired TLS certificate');
        evidence.push({
          type: 'text',
          description: 'TLS certificate validation failed',
          value: tlsInfo.error || 'Certificate invalid',
        });
      }

      // Check for HSTS header
      const hstsCheck = await this.checkHstsHeader(config.targetUrl);
      if (!hstsCheck.present) {
        passed = false;
        issues.push('Missing Strict-Transport-Security header');
        evidence.push({
          type: 'network',
          description: 'HSTS header check',
          value: 'Header not found',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'HSTS header found',
          value: hstsCheck.value || 'Present',
        });
      }

      return {
        ruleId: 'GDPR-001',
        ruleName: 'HTTPS/TLS Encryption',
        category: 'security',
        passed,
        score: passed ? 100 : 0,
        weight: 8,
        severity: 'critical',
        evidence,
        recommendations: passed ? [] : [
          {
            priority: 1,
            title: 'Implement HTTPS encryption',
            description: 'Enable HTTPS with valid TLS certificate and HSTS header',
            implementation: 'Configure web server with TLS certificate and security headers',
            effort: 'moderate',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-001',
        ruleName: 'HTTPS/TLS Encryption',
        category: 'security',
        passed: false,
        score: 0,
        weight: 8,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Fix HTTPS configuration',
          description: 'Resolve HTTPS/TLS configuration issues',
          implementation: 'Check server configuration and certificate validity',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    }
  }

  /**
   * Analyze TLS certificate - REAL certificate validation
   */
  private async analyzeTlsCertificate(hostname: string, port: string): Promise<{
    valid: boolean;
    error?: string;
    certificate?: Record<string, unknown>;
  }> {
    return new Promise((resolve) => {
      const options = {
        hostname,
        port: parseInt(port),
        method: 'GET',
        rejectUnauthorized: false, // We want to check the cert ourselves
      };

      const req = https.request(options, (res) => {
        const cert = (res.socket as TLSSocket).getPeerCertificate();
        
        if (!cert || Object.keys(cert).length === 0) {
          resolve({ valid: false, error: 'No certificate found' });
          return;
        }

        // Check certificate validity
        const now = new Date();
        const validFrom = new Date(cert.valid_from);
        const validTo = new Date(cert.valid_to);

        if (now < validFrom || now > validTo) {
          resolve({ 
            valid: false, 
            error: `Certificate expired or not yet valid. Valid from ${validFrom} to ${validTo}` 
          });
          return;
        }

        resolve({ valid: true, certificate: cert as unknown as Record<string, unknown> });
      });

      req.on('error', (error) => {
        resolve({ valid: false, error: error.message });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        resolve({ valid: false, error: 'Connection timeout' });
      });

      req.end();
    });
  }

  /**
   * Check for HSTS header - REAL HTTP request
   */
  private async checkHstsHeader(url: string): Promise<{
    present: boolean;
    value?: string;
  }> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        redirect: 'follow',
      });

      const hstsHeader = response.headers.get('strict-transport-security');
      
      return {
        present: !!hstsHeader,
        value: hstsHeader || undefined,
      };
    } catch (error) {
      return { present: false };
    }
  }
}
