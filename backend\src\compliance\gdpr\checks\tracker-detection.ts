import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface TrackerDetectionCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class TrackerDetectionCheck {
  /**
   * Detect third-party trackers and analyze their GDPR compliance
   * REAL ANALYSIS - analyzes actual network requests and scripts
   */
  async performCheck(config: TrackerDetectionCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      // Track network requests
      const networkRequests: Array<{
        url: string;
        domain: string;
        type: string;
        isThirdParty: boolean;
      }> = [];

      page.on('request', (request) => {
        const url = request.url();
        const domain = this.extractDomain(url);
        const targetDomain = this.extractDomain(config.targetUrl);
        
        networkRequests.push({
          url,
          domain,
          type: request.resourceType(),
          isThirdParty: domain !== targetDomain,
        });
      });

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze trackers
      const trackerAnalysis = this.analyzeTrackers(networkRequests);
      
      // Check for tracking scripts in page content
      const scriptAnalysis = await this.analyzeTrackingScripts(page);
      
      // Combine analyses
      const allTrackers = [...trackerAnalysis.knownTrackers, ...scriptAnalysis.trackingScripts];
      const uniqueTrackers = this.deduplicateTrackers(allTrackers);

      // Calculate compliance score
      score = this.calculateTrackerScore(uniqueTrackers, trackerAnalysis.totalThirdPartyRequests);

      // Generate evidence
      evidence.push({
        type: 'text',
        description: 'Third-party requests detected',
        value: `${trackerAnalysis.totalThirdPartyRequests} third-party requests found`,
      });

      evidence.push({
        type: 'text',
        description: 'Known trackers identified',
        value: `${uniqueTrackers.length} tracking services detected`,
      });

      // Add specific tracker evidence
      for (const tracker of uniqueTrackers.slice(0, 10)) { // Limit to first 10
        evidence.push({
          type: 'text',
          description: `Tracker: ${tracker.name}`,
          value: `Category: ${tracker.category}, Risk: ${tracker.riskLevel}`,
        });
      }

      const passed = score >= 60; // Lower threshold as trackers are common

      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Trackers',
        category: 'data_sharing',
        passed,
        score,
        weight: 6,
        severity: 'medium',
        evidence,
        recommendations: this.generateTrackerRecommendations(uniqueTrackers),
        manualReviewRequired: true, // Requires review of data sharing agreements
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Trackers',
        category: 'data_sharing',
        passed: false,
        score: 0,
        weight: 6,
        severity: 'medium',
        evidence: [{
          type: 'text',
          description: 'Tracker detection failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Audit third-party services',
          description: 'Review all third-party integrations for GDPR compliance',
          implementation: 'Conduct manual audit of tracking services and data sharing',
          effort: 'moderate',
          impact: 'medium',
        }],
        manualReviewRequired: true,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * Analyze network requests for known trackers
   */
  private analyzeTrackers(requests: Array<{
    url: string;
    domain: string;
    type: string;
    isThirdParty: boolean;
  }>): {
    knownTrackers: Array<{
      name: string;
      domain: string;
      category: string;
      riskLevel: string;
    }>;
    totalThirdPartyRequests: number;
  } {
    const knownTrackers: Array<{
      name: string;
      domain: string;
      category: string;
      riskLevel: string;
    }> = [];

    const trackerDatabase = [
      { domain: 'google-analytics.com', name: 'Google Analytics', category: 'Analytics', risk: 'medium' },
      { domain: 'googletagmanager.com', name: 'Google Tag Manager', category: 'Analytics', risk: 'medium' },
      { domain: 'facebook.com', name: 'Facebook Pixel', category: 'Marketing', risk: 'high' },
      { domain: 'doubleclick.net', name: 'Google DoubleClick', category: 'Advertising', risk: 'high' },
      { domain: 'hotjar.com', name: 'Hotjar', category: 'Analytics', risk: 'medium' },
      { domain: 'mixpanel.com', name: 'Mixpanel', category: 'Analytics', risk: 'medium' },
      { domain: 'linkedin.com', name: 'LinkedIn Insight', category: 'Marketing', risk: 'medium' },
      { domain: 'twitter.com', name: 'Twitter Analytics', category: 'Marketing', risk: 'medium' },
      { domain: 'amazon-adsystem.com', name: 'Amazon Advertising', category: 'Advertising', risk: 'high' },
      { domain: 'adsystem.com', name: 'Ad System', category: 'Advertising', risk: 'high' },
    ];

    const thirdPartyRequests = requests.filter(req => req.isThirdParty);

    for (const request of thirdPartyRequests) {
      for (const tracker of trackerDatabase) {
        if (request.domain.includes(tracker.domain) || request.url.includes(tracker.domain)) {
          knownTrackers.push({
            name: tracker.name,
            domain: tracker.domain,
            category: tracker.category,
            riskLevel: tracker.risk,
          });
        }
      }
    }

    return {
      knownTrackers,
      totalThirdPartyRequests: thirdPartyRequests.length,
    };
  }

  /**
   * Analyze page scripts for tracking code
   */
  private async analyzeTrackingScripts(page: Page): Promise<{
    trackingScripts: Array<{
      name: string;
      domain: string;
      category: string;
      riskLevel: string;
    }>;
  }> {
    return await page.evaluate(() => {
      const trackingScripts: Array<{
        name: string;
        domain: string;
        category: string;
        riskLevel: string;
      }> = [];

      const scripts = Array.from(document.querySelectorAll('script'));
      
      const trackingPatterns = [
        { pattern: /gtag|ga\(|google.*analytics/i, name: 'Google Analytics', category: 'Analytics', risk: 'medium' },
        { pattern: /fbq|facebook.*pixel/i, name: 'Facebook Pixel', category: 'Marketing', risk: 'high' },
        { pattern: /hotjar/i, name: 'Hotjar', category: 'Analytics', risk: 'medium' },
        { pattern: /mixpanel/i, name: 'Mixpanel', category: 'Analytics', risk: 'medium' },
        { pattern: /linkedin.*insight/i, name: 'LinkedIn Insight', category: 'Marketing', risk: 'medium' },
        { pattern: /twitter.*analytics/i, name: 'Twitter Analytics', category: 'Marketing', risk: 'medium' },
      ];

      for (const script of scripts) {
        const content = script.textContent || '';
        const src = script.src || '';
        const fullContent = content + ' ' + src;

        for (const tracker of trackingPatterns) {
          if (tracker.pattern.test(fullContent)) {
            trackingScripts.push({
              name: tracker.name,
              domain: 'detected-in-script',
              category: tracker.category,
              riskLevel: tracker.risk,
            });
          }
        }
      }

      return { trackingScripts };
    });
  }

  /**
   * Remove duplicate trackers
   */
  private deduplicateTrackers(trackers: Array<{
    name: string;
    domain: string;
    category: string;
    riskLevel: string;
  }>): Array<{
    name: string;
    domain: string;
    category: string;
    riskLevel: string;
  }> {
    const seen = new Set<string>();
    return trackers.filter(tracker => {
      const key = tracker.name;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Calculate tracker compliance score
   */
  private calculateTrackerScore(
    trackers: Array<{ riskLevel: string }>,
    totalThirdPartyRequests: number
  ): number {
    if (trackers.length === 0) {
      return 100; // No trackers = perfect score
    }

    let score = 100;
    
    // Deduct points for high-risk trackers
    const highRiskTrackers = trackers.filter(t => t.riskLevel === 'high').length;
    const mediumRiskTrackers = trackers.filter(t => t.riskLevel === 'medium').length;
    
    score -= highRiskTrackers * 20; // 20 points per high-risk tracker
    score -= mediumRiskTrackers * 10; // 10 points per medium-risk tracker
    
    // Additional deduction for excessive third-party requests
    if (totalThirdPartyRequests > 50) {
      score -= 10;
    }
    
    return Math.max(0, score);
  }

  /**
   * Generate tracker-specific recommendations
   */
  private generateTrackerRecommendations(trackers: Array<{
    name: string;
    category: string;
    riskLevel: string;
  }>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const highRiskTrackers = trackers.filter(t => t.riskLevel === 'high');
    if (highRiskTrackers.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Review high-risk trackers',
        description: `${highRiskTrackers.length} high-risk tracking services detected`,
        implementation: 'Ensure proper consent mechanisms and data processing agreements for high-risk trackers',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (trackers.length > 0) {
      recommendations.push({
        priority: 2,
        title: 'Update privacy policy for third-party services',
        description: 'Document all third-party tracking services in privacy policy',
        implementation: 'List all detected tracking services and their purposes in privacy policy',
        effort: 'minimal',
        impact: 'medium',
      });
    }

    recommendations.push({
      priority: 3,
      title: 'Implement consent-based loading',
      description: 'Load tracking scripts only after user consent',
      implementation: 'Use consent management platform to control tracker loading',
      effort: 'moderate',
      impact: 'high',
    });

    return recommendations;
  }
}
