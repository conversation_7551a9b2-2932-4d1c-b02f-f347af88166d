import puppeteer, { <PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface ChildrenConsentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class ChildrenConsentCheck {
  /**
   * Check children's consent mechanisms and age verification
   * REAL ANALYSIS - scans for age-related content and verification
   */
  async performCheck(config: ChildrenConsentCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze for children-related content and age verification
      const childrenAnalysis = await this.analyzeChildrenContent(page);
      
      // Add evidence for detected age-related elements
      if (childrenAnalysis.ageKeywords.length > 0) {
        evidence.push({
          type: 'text',
          description: 'Age-related content detected',
          value: `Keywords found: ${childrenAnalysis.ageKeywords.join(', ')}`,
        });
      }

      if (childrenAnalysis.hasAgeVerification) {
        evidence.push({
          type: 'element',
          description: 'Age verification mechanism detected',
          value: childrenAnalysis.ageVerificationDetails,
        });
      } else if (childrenAnalysis.ageKeywords.length > 0) {
        evidence.push({
          type: 'element',
          description: 'Age-related content found but no verification mechanism',
          value: 'May require age verification for GDPR compliance',
        });
      }

      if (childrenAnalysis.hasParentalConsent) {
        evidence.push({
          type: 'element',
          description: 'Parental consent mechanism detected',
          value: childrenAnalysis.parentalConsentDetails,
        });
      }

      if (childrenAnalysis.hasMinimumAge) {
        evidence.push({
          type: 'text',
          description: 'Minimum age requirement mentioned',
          value: childrenAnalysis.minimumAgeDetails,
        });
      }

      // Check for educational content or child-directed services
      const serviceAnalysis = await this.analyzeServiceType(page);
      
      if (serviceAnalysis.isChildDirected) {
        evidence.push({
          type: 'text',
          description: 'Child-directed service indicators found',
          value: `Indicators: ${serviceAnalysis.indicators.join(', ')}`,
        });
      }

      // Always requires manual review due to legal complexity
      evidence.push({
        type: 'text',
        description: 'Manual review required',
        value: 'Children\'s consent mechanisms require legal expertise assessment',
      });

      return {
        ruleId: 'GDPR-014',
        ruleName: "Children's Data Consent",
        category: 'consent',
        passed: false, // Always requires manual review
        score: 0, // Score not applicable for manual review items
        weight: 0, // No weight in automated scoring
        severity: 'high',
        evidence,
        recommendations: this.generateRecommendations(childrenAnalysis, serviceAnalysis),
        manualReviewRequired: true, // Always requires manual legal review
      };

    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze page for children-related content and age verification
   */
  private async analyzeChildrenContent(page: Page): Promise<{
    ageKeywords: string[];
    hasAgeVerification: boolean;
    ageVerificationDetails: string;
    hasParentalConsent: boolean;
    parentalConsentDetails: string;
    hasMinimumAge: boolean;
    minimumAgeDetails: string;
  }> {
    return await page.evaluate(() => {
      const text = document.body.textContent?.toLowerCase() || '';
      
      const ageKeywords = [
        'age', 'child', 'children', 'minor', 'under 13', 'under 16', 
        'parental consent', 'parent permission', 'guardian consent',
        'age verification', 'date of birth', 'birth date'
      ];
      
      const foundKeywords = ageKeywords.filter(keyword => text.includes(keyword));

      // Look for age verification forms
      const forms = Array.from(document.querySelectorAll('form'));
      let hasAgeVerification = false;
      let ageVerificationDetails = '';

      for (const form of forms) {
        const formText = form.textContent?.toLowerCase() || '';
        const inputs = Array.from(form.querySelectorAll('input, select'));
        
        const hasAgeInput = inputs.some(input => {
          const name = (input as HTMLInputElement).name?.toLowerCase() || '';
          const id = input.id?.toLowerCase() || '';
          const placeholder = (input as HTMLInputElement).placeholder?.toLowerCase() || '';
          
          return name.includes('age') || name.includes('birth') || 
                 id.includes('age') || id.includes('birth') ||
                 placeholder.includes('age') || placeholder.includes('birth');
        });

        if (hasAgeInput || formText.includes('age') || formText.includes('birth')) {
          hasAgeVerification = true;
          ageVerificationDetails = 'Forms with age/birth date inputs found';
          break;
        }
      }

      // Look for parental consent mechanisms
      let hasParentalConsent = false;
      let parentalConsentDetails = '';
      
      const parentalKeywords = ['parental consent', 'parent permission', 'guardian consent', 'parent approval'];
      const foundParentalKeywords = parentalKeywords.filter(keyword => text.includes(keyword));
      
      if (foundParentalKeywords.length > 0) {
        hasParentalConsent = true;
        parentalConsentDetails = `Found: ${foundParentalKeywords.join(', ')}`;
      }

      // Look for minimum age requirements
      let hasMinimumAge = false;
      let minimumAgeDetails = '';
      
      const agePatterns = [
        /under (\d+)/g,
        /minimum age (\d+)/g,
        /must be (\d+)/g,
        /(\d+) years old/g
      ];
      
      for (const pattern of agePatterns) {
        const matches = text.match(pattern);
        if (matches) {
          hasMinimumAge = true;
          minimumAgeDetails = `Age requirements: ${matches.join(', ')}`;
          break;
        }
      }

      return {
        ageKeywords: foundKeywords,
        hasAgeVerification,
        ageVerificationDetails,
        hasParentalConsent,
        parentalConsentDetails,
        hasMinimumAge,
        minimumAgeDetails,
      };
    });
  }

  /**
   * Analyze if service is child-directed
   */
  private async analyzeServiceType(page: Page): Promise<{
    isChildDirected: boolean;
    indicators: string[];
  }> {
    return await page.evaluate(() => {
      const text = document.body.textContent?.toLowerCase() || '';
      
      const childDirectedIndicators = [
        'kids', 'children', 'child', 'student', 'school', 'education',
        'learning', 'homework', 'teacher', 'classroom', 'grade',
        'elementary', 'primary', 'kindergarten', 'preschool',
        'toy', 'game', 'cartoon', 'animation'
      ];
      
      const foundIndicators = childDirectedIndicators.filter(indicator => 
        text.includes(indicator)
      );

      return {
        isChildDirected: foundIndicators.length >= 3, // Threshold for child-directed
        indicators: foundIndicators,
      };
    });
  }

  /**
   * Generate recommendations for children's consent compliance
   */
  private generateRecommendations(
    childrenAnalysis: any,
    serviceAnalysis: any
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Always add manual review recommendation
    recommendations.push({
      priority: 1,
      title: 'Manual legal review required',
      description: 'Children\'s consent mechanisms require legal expertise assessment',
      implementation: 'Have legal expert review age verification and parental consent processes',
      effort: 'moderate',
      impact: 'high',
    });

    if (childrenAnalysis.ageKeywords.length > 0 && !childrenAnalysis.hasAgeVerification) {
      recommendations.push({
        priority: 2,
        title: 'Implement age verification',
        description: 'Add age verification mechanism for users under 16',
        implementation: 'Create age verification form or process',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (serviceAnalysis.isChildDirected && !childrenAnalysis.hasParentalConsent) {
      recommendations.push({
        priority: 3,
        title: 'Add parental consent mechanism',
        description: 'Child-directed services require parental consent for data processing',
        implementation: 'Implement parental consent verification system',
        effort: 'significant',
        impact: 'high',
      });
    }

    if (!childrenAnalysis.hasMinimumAge) {
      recommendations.push({
        priority: 4,
        title: 'Clarify minimum age requirements',
        description: 'Specify minimum age for service use in privacy policy',
        implementation: 'Add clear age requirements to terms and privacy policy',
        effort: 'minimal',
        impact: 'medium',
      });
    }

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-014',
      ruleName: "Children's Data Consent",
      category: 'consent',
      passed: false,
      score: 0,
      weight: 0,
      severity: 'high',
      evidence: [{
        type: 'text',
        description: 'Children consent check failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }],
      recommendations: [{
        priority: 1,
        title: 'Manual review required',
        description: 'Legal assessment needed for children\'s consent compliance',
        implementation: 'Consult legal expert for GDPR Article 8 compliance',
        effort: 'moderate',
        impact: 'high',
      }],
      manualReviewRequired: true,
    };
  }
}
