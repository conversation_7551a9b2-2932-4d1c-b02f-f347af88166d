import puppeteer, { <PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface FormConsentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class FormConsentCheck {
  /**
   * Check data-collecting forms for consent controls
   * REAL ANALYSIS - analyzes actual forms on website
   */
  async performCheck(config: FormConsentCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Find and analyze forms
      const formsAnalysis = await this.analyzeForms(page);
      
      if (formsAnalysis.totalForms === 0) {
        evidence.push({
          type: 'element',
          description: 'No forms found on page',
          value: 'No form consent concerns',
        });
        score = 100; // No forms = no consent issues
      } else {
        const dataCollectingForms = formsAnalysis.forms.filter(f => f.collectsPersonalData);
        
        if (dataCollectingForms.length === 0) {
          score = 90; // Forms exist but don't collect personal data
          evidence.push({
            type: 'element',
            description: 'Forms found but no personal data collection detected',
            value: `${formsAnalysis.totalForms} forms analyzed`,
          });
        } else {
          // Calculate score based on consent compliance
          const compliantForms = dataCollectingForms.filter(f => f.hasConsentMechanism);
          score = Math.round((compliantForms.length / dataCollectingForms.length) * 100);

          evidence.push({
            type: 'element',
            description: 'Data-collecting forms analysis',
            value: `${compliantForms.length}/${dataCollectingForms.length} forms have consent mechanisms`,
          });

          // Add detailed evidence for each form
          for (const form of dataCollectingForms) {
            if (form.hasConsentMechanism) {
              evidence.push({
                type: 'element',
                description: `Form with consent: ${form.description}`,
                value: `Consent type: ${form.consentType}`,
                location: form.location,
              });
            } else {
              evidence.push({
                type: 'element',
                description: `Form without consent: ${form.description}`,
                value: 'Missing consent mechanism',
                location: form.location,
              });
            }
          }
        }
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-009',
        ruleName: 'Data-Collecting Forms & Consent Controls',
        category: 'consent',
        passed,
        score,
        weight: 6,
        severity: 'medium',
        evidence,
        recommendations: this.generateRecommendations(formsAnalysis, score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze forms on the page for data collection and consent
   */
  private async analyzeForms(page: Page): Promise<{
    totalForms: number;
    forms: Array<{
      description: string;
      location: string;
      collectsPersonalData: boolean;
      hasConsentMechanism: boolean;
      consentType?: string;
      personalDataFields: string[];
    }>;
  }> {
    return await page.evaluate(() => {
      const forms = Array.from(document.querySelectorAll('form'));
      const personalDataPatterns = [
        'email', 'name', 'phone', 'address', 'birth', 'age',
        'firstname', 'lastname', 'surname', 'contact', 'personal'
      ];

      const formAnalysis = forms.map((form, index) => {
        const inputs = Array.from(form.querySelectorAll('input, textarea, select'));
        const personalDataFields: string[] = [];
        
        // Check for personal data collection
        inputs.forEach(input => {
          const name = (input as HTMLInputElement).name?.toLowerCase() || '';
          const id = input.id?.toLowerCase() || '';
          const placeholder = (input as HTMLInputElement).placeholder?.toLowerCase() || '';
          const label = form.querySelector(`label[for="${input.id}"]`)?.textContent?.toLowerCase() || '';
          
          const fieldText = `${name} ${id} ${placeholder} ${label}`;
          
          if (personalDataPatterns.some(pattern => fieldText.includes(pattern))) {
            personalDataFields.push(name || id || `input-${inputs.indexOf(input)}`);
          }
        });

        const collectsPersonalData = personalDataFields.length > 0;

        // Check for consent mechanisms
        const checkboxes = Array.from(form.querySelectorAll('input[type="checkbox"]'));
        const consentCheckboxes = checkboxes.filter(checkbox => {
          const label = form.querySelector(`label[for="${checkbox.id}"]`)?.textContent?.toLowerCase() || '';
          const parentText = checkbox.parentElement?.textContent?.toLowerCase() || '';
          const combinedText = label + ' ' + parentText;
          
          return combinedText.includes('consent') || 
                 combinedText.includes('agree') || 
                 combinedText.includes('privacy') ||
                 combinedText.includes('terms');
        });

        let hasConsentMechanism = false;
        let consentType = '';

        if (consentCheckboxes.length > 0) {
          hasConsentMechanism = true;
          consentType = 'checkbox';
        } else {
          // Check for other consent mechanisms
          const submitButton = form.querySelector('input[type="submit"], button[type="submit"], button');
          const submitText = submitButton?.textContent?.toLowerCase() || '';
          
          if (submitText.includes('agree') || submitText.includes('consent')) {
            hasConsentMechanism = true;
            consentType = 'submit button';
          }
        }

        // Determine form location
        let location = 'main content';
        const parent = form.closest('header, footer, nav, aside, .header, .footer, .sidebar');
        if (parent) {
          location = parent.tagName.toLowerCase();
        }

        return {
          description: `Form ${index + 1}`,
          location,
          collectsPersonalData,
          hasConsentMechanism,
          consentType: hasConsentMechanism ? consentType : undefined,
          personalDataFields,
        };
      });

      return {
        totalForms: forms.length,
        forms: formAnalysis,
      };
    });
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(
    analysis: { totalForms: number; forms: any[] },
    score: number
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 70) {
      const dataFormsWithoutConsent = analysis.forms.filter(
        f => f.collectsPersonalData && !f.hasConsentMechanism
      );

      if (dataFormsWithoutConsent.length > 0) {
        recommendations.push({
          priority: 1,
          title: 'Add consent mechanisms to data-collecting forms',
          description: `${dataFormsWithoutConsent.length} forms collect personal data without consent`,
          implementation: 'Add consent checkboxes or clear consent language to forms that collect personal data',
          effort: 'moderate',
          impact: 'high',
        });
      }
    }

    if (score < 50) {
      recommendations.push({
        priority: 2,
        title: 'Review form data collection practices',
        description: 'Minimize personal data collection and ensure all collection is necessary',
        implementation: 'Audit forms to remove unnecessary personal data fields',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-009',
      ruleName: 'Data-Collecting Forms & Consent Controls',
      category: 'consent',
      passed: false,
      score: 0,
      weight: 6,
      severity: 'medium',
      evidence: [{
        type: 'text',
        description: 'Form consent check failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }],
      recommendations: [{
        priority: 1,
        title: 'Implement form consent controls',
        description: 'Add consent mechanisms to data-collecting forms',
        implementation: 'Review and update form consent handling',
        effort: 'moderate',
        impact: 'high',
      }],
      manualReviewRequired: false,
    };
  }
}
