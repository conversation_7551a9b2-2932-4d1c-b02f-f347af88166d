/**
 * GDPR Integration Test
 * 
 * End-to-end test to verify the complete GDPR implementation works
 * including orchestrator, database, and all 21 checks.
 */

import { GdprOrchestrator } from '../orchestrator';
import { GdprScanRequest, GdprScanResult } from '../types';
import { GdprDatabase } from '../database/gdpr-database';

describe('GDPR Complete Integration Test', () => {
  let orchestrator: GdprOrchestrator;

  beforeEach(() => {
    orchestrator = new GdprOrchestrator();
  });

  test('should execute complete GDPR scan with all 21 checks', async () => {
    const scanRequest: GdprScanRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        timeout: 15000, // 15 seconds for testing
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'GDPR-Test-Scanner/1.0',
      },
    };

    console.log('🚀 Starting comprehensive GDPR scan test...');
    const startTime = Date.now();

    const result: GdprScanResult = await orchestrator.performComprehensiveScan(
      'test-user-integration',
      scanRequest
    );

    const duration = Date.now() - startTime;
    console.log(`✅ GDPR scan completed in ${duration}ms`);

    // Verify basic result structure
    expect(result).toBeDefined();
    expect(result.scanId).toBeDefined();
    expect(result.targetUrl).toBe(scanRequest.targetUrl);
    expect(result.timestamp).toBeDefined();
    expect(result.scanDuration).toBeGreaterThan(0);
    expect(result.status).toBe('completed');

    // Verify all 21 checks are present
    expect(result.checks).toBeDefined();
    expect(Array.isArray(result.checks)).toBe(true);
    expect(result.checks.length).toBe(21);

    // Verify all expected rule IDs are present
    const ruleIds = result.checks.map(check => check.ruleId).sort();
    const expectedRuleIds = [
      'GDPR-001', 'GDPR-002', 'GDPR-003', 'GDPR-004', 'GDPR-005',
      'GDPR-006', 'GDPR-007', 'GDPR-008', 'GDPR-009', 'GDPR-010',
      'GDPR-011', 'GDPR-012', 'GDPR-013', 'GDPR-014', 'GDPR-015',
      'GDPR-016', 'GDPR-017', 'GDPR-018', 'GDPR-019', 'GDPR-020',
      'GDPR-021'
    ];
    expect(ruleIds).toEqual(expectedRuleIds);

    // Verify each check has required properties
    for (const check of result.checks) {
      expect(check.ruleId).toBeDefined();
      expect(check.ruleName).toBeDefined();
      expect(check.category).toBeDefined();
      expect(typeof check.passed).toBe('boolean');
      expect(typeof check.score).toBe('number');
      expect(typeof check.weight).toBe('number');
      expect(check.severity).toBeDefined();
      expect(Array.isArray(check.evidence)).toBe(true);
      expect(Array.isArray(check.recommendations)).toBe(true);
      expect(typeof check.manualReviewRequired).toBe('boolean');
    }

    // Verify summary calculations
    expect(result.summary).toBeDefined();
    expect(result.summary.totalChecks).toBe(21);
    expect(result.summary.passedChecks + result.summary.failedChecks).toBe(21);
    expect(result.summary.categoryBreakdown).toBeDefined();
    expect(Array.isArray(result.summary.categoryBreakdown)).toBe(true);

    // Verify scoring
    expect(typeof result.overallScore).toBe('number');
    expect(result.overallScore).toBeGreaterThanOrEqual(0);
    expect(result.overallScore).toBeLessThanOrEqual(100);
    expect(['critical', 'high', 'medium', 'low']).toContain(result.riskLevel);

    // Verify metadata
    expect(result.metadata).toBeDefined();
    expect(result.metadata.version).toBeDefined();
    expect(result.metadata.processingTime).toBeGreaterThan(0);
    expect(result.metadata.checksPerformed).toBe(21);

    console.log(`📊 GDPR Scan Results Summary:`);
    console.log(`   Overall Score: ${result.overallScore}%`);
    console.log(`   Risk Level: ${result.riskLevel}`);
    console.log(`   Passed Checks: ${result.summary.passedChecks}/21`);
    console.log(`   Failed Checks: ${result.summary.failedChecks}/21`);
    console.log(`   Manual Review: ${result.summary.manualReviewRequired}/21`);

  }, 60000); // 60 second timeout

  test('should handle manual review checks correctly', async () => {
    const scanRequest: GdprScanRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        timeout: 10000,
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'GDPR-Test-Scanner/1.0',
      },
    };

    const result = await orchestrator.performComprehensiveScan('test-user-manual', scanRequest);

    // Find manual review checks
    const manualReviewChecks = result.checks.filter(check => check.manualReviewRequired);
    
    console.log(`🔍 Manual Review Checks Found: ${manualReviewChecks.length}`);
    manualReviewChecks.forEach(check => {
      console.log(`   - ${check.ruleId}: ${check.ruleName}`);
    });

    // Should have at least the known manual review checks
    const expectedManualReviewRules = ['GDPR-013', 'GDPR-014', 'GDPR-016', 'GDPR-018'];
    
    for (const ruleId of expectedManualReviewRules) {
      const check = result.checks.find(c => c.ruleId === ruleId);
      expect(check).toBeDefined();
      expect(check?.manualReviewRequired).toBe(true);
    }

    expect(manualReviewChecks.length).toBeGreaterThanOrEqual(4);

  }, 45000);

  test('should verify specific check implementations', async () => {
    const scanRequest: GdprScanRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        timeout: 10000,
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'GDPR-Test-Scanner/1.0',
      },
    };

    const result = await orchestrator.performComprehensiveScan('test-user-specific', scanRequest);

    // Test specific checks
    const httpsCheck = result.checks.find(c => c.ruleId === 'GDPR-001');
    expect(httpsCheck).toBeDefined();
    expect(httpsCheck?.ruleName).toBe('HTTPS/TLS Encryption');
    expect(httpsCheck?.category).toBe('security');

    const privacyPolicyCheck = result.checks.find(c => c.ruleId === 'GDPR-002');
    expect(privacyPolicyCheck).toBeDefined();
    expect(privacyPolicyCheck?.ruleName).toBe('Privacy Policy Presence');
    expect(privacyPolicyCheck?.category).toBe('privacy_policy');

    const cookieConsentCheck = result.checks.find(c => c.ruleId === 'GDPR-004');
    expect(cookieConsentCheck).toBeDefined();
    expect(cookieConsentCheck?.ruleName).toBe('Cookie Consent Banner');
    expect(cookieConsentCheck?.category).toBe('consent');

    // Test new Part 3C checks
    const cookieAttributesCheck = result.checks.find(c => c.ruleId === 'GDPR-007');
    expect(cookieAttributesCheck).toBeDefined();
    expect(cookieAttributesCheck?.ruleName).toBe('Cookie Security Attributes');

    const gpcCheck = result.checks.find(c => c.ruleId === 'GDPR-008');
    expect(gpcCheck).toBeDefined();
    expect(gpcCheck?.ruleName).toBe('Global Privacy Control/Do-Not-Track');

    const formConsentCheck = result.checks.find(c => c.ruleId === 'GDPR-009');
    expect(formConsentCheck).toBeDefined();
    expect(formConsentCheck?.ruleName).toBe('Data-Collecting Forms & Consent Controls');

    const dpoCheck = result.checks.find(c => c.ruleId === 'GDPR-015');
    expect(dpoCheck).toBeDefined();
    expect(dpoCheck?.ruleName).toBe('Data Protection Officer/EU Representative');

    console.log('✅ All specific check implementations verified');

  }, 45000);

  test('should handle errors gracefully', async () => {
    const invalidScanRequest: GdprScanRequest = {
      targetUrl: 'https://invalid-domain-that-does-not-exist-12345.com',
      scanOptions: {
        timeout: 5000, // Short timeout to fail quickly
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 1,
        userAgent: 'GDPR-Test-Scanner/1.0',
      },
    };

    // This should not throw an error, but should handle failures gracefully
    const result = await orchestrator.performComprehensiveScan('test-user-error', invalidScanRequest);

    expect(result).toBeDefined();
    expect(result.checks).toBeDefined();
    expect(result.checks.length).toBe(21); // Should still have all checks

    // Some checks may fail, but the scan should complete
    expect(result.status).toBe('completed');

    console.log('✅ Error handling verified - scan completed despite invalid URL');

  }, 30000);
});
