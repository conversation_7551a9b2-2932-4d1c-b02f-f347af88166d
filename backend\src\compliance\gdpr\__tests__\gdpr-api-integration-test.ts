/**
 * GDPR API Integration Test
 * 
 * Test the GDPR API endpoints to verify complete integration works.
 * This test focuses on API validation and structure without database dependencies.
 */

import request from 'supertest';
import express from 'express';
import gdprRouter from '../../../routes/compliance/gdpr';

// Mock the database to avoid connection issues in tests
jest.mock('../database/gdpr-database', () => ({
  GdprDatabase: {
    createScan: jest.fn().mockResolvedValue('mock-scan-id'),
    updateScanStatus: jest.fn().mockResolvedValue(undefined),
    saveScanResult: jest.fn().mockResolvedValue(undefined),
    getScanResult: jest.fn().mockResolvedValue({
      scanId: 'mock-scan-id',
      targetUrl: 'https://example.com',
      status: 'completed',
      overallScore: 85,
      riskLevel: 'medium',
      checks: [],
      summary: { totalChecks: 21, passedChecks: 18, failedChecks: 3 },
      timestamp: new Date().toISOString(),
    }),
    getUserScans: jest.fn().mockResolvedValue([]),
  },
}));

// Mock the orchestrator to avoid actual website scanning in tests
jest.mock('../orchestrator', () => ({
  GdprOrchestrator: jest.fn().mockImplementation(() => ({
    performComprehensiveScan: jest.fn().mockResolvedValue({
      scanId: 'mock-scan-id',
      targetUrl: 'https://example.com',
      status: 'completed',
      overallScore: 85,
      riskLevel: 'medium',
      checks: Array.from({ length: 21 }, (_, i) => ({
        ruleId: `GDPR-${String(i + 1).padStart(3, '0')}`,
        ruleName: `Test Rule ${i + 1}`,
        category: 'test',
        passed: true,
        score: 85,
        weight: 5,
        severity: 'medium',
        evidence: [],
        recommendations: [],
        manualReviewRequired: false,
      })),
      summary: {
        totalChecks: 21,
        passedChecks: 18,
        failedChecks: 3,
        manualReviewRequired: 2,
        categoryBreakdown: [],
      },
      metadata: {
        version: '1.0.0',
        processingTime: 5000,
        checksPerformed: 21,
      },
      timestamp: new Date().toISOString(),
      scanDuration: 5000,
    }),
  })),
}));

describe('GDPR API Integration Test', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      (req as any).user = { id: 'test-user' };
      next();
    });
    
    app.use('/api/v1/compliance/gdpr', gdprRouter);
  });

  test('should validate GDPR scan request structure', async () => {
    const validRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
        maxPages: 5,
        timeout: 300000,
      },
    };

    const response = await request(app)
      .post('/api/v1/compliance/gdpr/scan')
      .send(validRequest)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.scanId).toBeDefined();
    expect(response.body.data.targetUrl).toBe(validRequest.targetUrl);
    expect(response.body.data.checks).toBeDefined();
    expect(response.body.data.checks.length).toBe(21);
  });

  test('should reject invalid URLs', async () => {
    const invalidRequest = {
      targetUrl: 'not-a-valid-url',
      scanOptions: {},
    };

    const response = await request(app)
      .post('/api/v1/compliance/gdpr/scan')
      .send(invalidRequest)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('Validation failed');
  });

  test('should validate scan options', async () => {
    const invalidRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        enableCookieAnalysis: 'not-a-boolean',
        maxPages: 999, // Too high
        timeout: 50000, // Too low
      },
    };

    const response = await request(app)
      .post('/api/v1/compliance/gdpr/scan')
      .send(invalidRequest)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('Validation failed');
  });

  test('should retrieve scan result by ID', async () => {
    const response = await request(app)
      .get('/api/v1/compliance/gdpr/scan/550e8400-e29b-41d4-a716-446655440000')
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.scanId).toBeDefined();
  });

  test('should reject invalid scan ID format', async () => {
    const response = await request(app)
      .get('/api/v1/compliance/gdpr/scan/invalid-id')
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('VALIDATION_ERROR');
  });

  test('should retrieve user scan history', async () => {
    const response = await request(app)
      .get('/api/v1/compliance/gdpr/scans')
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.scans).toBeDefined();
    expect(response.body.data.pagination).toBeDefined();
  });

  test('should validate pagination parameters', async () => {
    const response = await request(app)
      .get('/api/v1/compliance/gdpr/scans?limit=999&offset=-1')
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('VALIDATION_ERROR');
  });

  test('should verify all 21 GDPR checks in response', async () => {
    const validRequest = {
      targetUrl: 'https://example.com',
      scanOptions: {
        enableCookieAnalysis: true,
        enableTrackerDetection: true,
        enableConsentTesting: true,
      },
    };

    const response = await request(app)
      .post('/api/v1/compliance/gdpr/scan')
      .send(validRequest)
      .expect(200);

    const { checks } = response.body.data;
    
    // Verify we have all 21 checks
    expect(checks.length).toBe(21);
    
    // Verify each check has required structure
    for (const check of checks) {
      expect(check.ruleId).toBeDefined();
      expect(check.ruleName).toBeDefined();
      expect(check.category).toBeDefined();
      expect(typeof check.passed).toBe('boolean');
      expect(typeof check.score).toBe('number');
      expect(typeof check.weight).toBe('number');
      expect(check.severity).toBeDefined();
      expect(Array.isArray(check.evidence)).toBe(true);
      expect(Array.isArray(check.recommendations)).toBe(true);
      expect(typeof check.manualReviewRequired).toBe('boolean');
    }
    
    // Verify summary structure
    const { summary } = response.body.data;
    expect(summary.totalChecks).toBe(21);
    expect(typeof summary.passedChecks).toBe('number');
    expect(typeof summary.failedChecks).toBe('number');
  });
});
