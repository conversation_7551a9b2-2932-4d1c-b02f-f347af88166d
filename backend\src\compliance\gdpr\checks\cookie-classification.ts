import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheck<PERSON><PERSON>ult, Evidence, Cookie, Recommendation } from '../types';
import { <PERSON>ieAnalyzer } from '../utils/cookie-analyzer';
import { GdprDatabase } from '../database/gdpr-database';

export interface CookieClassificationCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
}

export class CookieClassificationCheck {
  /**
   * Check cookie classification and consent-based blocking
   * REAL COOKIE MONITORING - analyzes actual website cookies
   */
  async performCheck(config: CookieClassificationCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      // Step 1: Monitor cookies before consent
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      await new Promise((resolve) => setTimeout(resolve, 3000)); // Allow time for initial cookies

      const cookiesBeforeConsent = await this.extractCookies(page);

      evidence.push({
        type: 'cookie',
        description: 'Cookies found before consent',
        value: `${cookiesBeforeConsent.length} cookies detected`,
      });

      // Step 2: Classify cookies
      const cookieClassification = CookieAnalyzer.classifyCookies(cookiesBeforeConsent);

      // Check if non-essential cookies are present before consent
      const nonEssentialBeforeConsent = [
        ...cookieClassification.analytics,
        ...cookieClassification.marketing,
        ...cookieClassification.functional,
      ];

      if (nonEssentialBeforeConsent.length > 0) {
        evidence.push({
          type: 'cookie',
          description: 'Non-essential cookies loaded before consent',
          value: `${nonEssentialBeforeConsent.length} non-essential cookies found`,
        });
        score -= 30; // Penalty for loading non-essential cookies without consent
      } else {
        evidence.push({
          type: 'cookie',
          description: 'Only essential cookies before consent',
          value: 'Compliant cookie loading',
        });
        score += 40;
      }

      // Step 3: Test consent interaction and monitor cookie changes
      const consentTestResult = await this.testConsentCookieBlocking(page);
      evidence.push(...consentTestResult.evidence);
      score += consentTestResult.score;

      // Step 4: Analyze cookie attributes
      const attributeAnalysis = CookieAnalyzer.analyzeCookieAttributes(cookiesBeforeConsent);
      const attributeScore = this.evaluateCookieAttributes(attributeAnalysis);
      evidence.push(...attributeScore.evidence);
      score += attributeScore.score;

      // Step 5: Save detailed cookie analysis to database
      await this.saveCookieAnalysisToDatabase(config.scanId, cookieClassification, attributeAnalysis);

      // Final scoring
      score = Math.max(0, Math.min(100, score + 30)); // Base score + adjustments
      const passed = score >= 70;

      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed,
        score,
        weight: 8,
        severity: 'critical',
        evidence,
        recommendations: this.generateCookieRecommendations(cookieClassification, score),
        manualReviewRequired: false,
      };
    } catch (error) {
      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed: false,
        score: 0,
        weight: 8,
        severity: 'critical',
        evidence: [
          {
            type: 'text',
            description: 'Cookie classification check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Implement proper cookie management',
            description: 'Set up cookie categorization and consent-based blocking',
            implementation: 'Use a cookie management platform to categorize and control cookies',
            effort: 'moderate',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Extract cookies from page - REAL cookie extraction
   */
  private async extractCookies(page: Page): Promise<Cookie[]> {
    const puppeteerCookies = await page.cookies();

    return puppeteerCookies.map((cookie) => ({
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite as 'Strict' | 'Lax' | 'None' | undefined,
      expires: cookie.expires ? new Date(cookie.expires * 1000) : undefined,
    }));
  }

  /**
   * Test consent-based cookie blocking - REAL consent interaction
   */
  private async testConsentCookieBlocking(page: Page): Promise<{
    score: number;
    evidence: Evidence[];
  }> {
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      // Look for consent banner
      const bannerExists = await page.evaluate(() => {
        const selectors = [
          '[class*="cookie"]',
          '[class*="consent"]',
          '[id*="cookie"]',
          '[id*="consent"]',
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          }
        }
        return false;
      });

      if (!bannerExists) {
        evidence.push({
          type: 'element',
          description: 'No consent banner found for cookie testing',
          value: 'Cannot test consent-based blocking',
        });
        return { score: 0, evidence };
      }

      // Try to reject cookies and monitor changes
      const rejectionResult = await page.evaluate(() => {
        const rejectButtons = document.querySelectorAll('button, a, input');
        for (const button of rejectButtons) {
          const text = button.textContent?.toLowerCase() || '';
          if (text.includes('reject') || text.includes('decline') || text.includes('deny')) {
            (button as HTMLElement).click();
            return true;
          }
        }
        return false;
      });

      if (rejectionResult) {
        await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for cookie changes

        const cookiesAfterRejection = await this.extractCookies(page);

        evidence.push({
          type: 'cookie',
          description: 'Tested cookie rejection',
          value: `${cookiesAfterRejection.length} cookies remain after rejection`,
        });

        // Check if non-essential cookies were properly blocked
        const classification = CookieAnalyzer.classifyCookies(cookiesAfterRejection);
        const nonEssentialAfterRejection = [
          ...classification.analytics,
          ...classification.marketing,
        ];

        if (nonEssentialAfterRejection.length === 0) {
          score += 30;
          evidence.push({
            type: 'cookie',
            description: 'Non-essential cookies properly blocked after rejection',
            value: 'Compliant cookie blocking',
          });
        } else {
          evidence.push({
            type: 'cookie',
            description: 'Non-essential cookies still present after rejection',
            value: `${nonEssentialAfterRejection.length} non-essential cookies not blocked`,
          });
        }
      }

    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Cookie consent testing failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return { score, evidence };
  }

  /**
   * Evaluate cookie attributes compliance
   */
  private evaluateCookieAttributes(analysis: any[]): {
    score: number;
    evidence: Evidence[];
  } {
    const evidence: Evidence[] = [];
    let score = 0;
    let totalCookies = analysis.length;
    let compliantCookies = 0;

    for (const cookieAnalysis of analysis) {
      if (cookieAnalysis.complianceIssues.length === 0) {
        compliantCookies++;
      } else {
        evidence.push({
          type: 'cookie',
          description: `Cookie attribute issues: ${cookieAnalysis.cookieId}`,
          value: cookieAnalysis.complianceIssues.join(', '),
        });
      }
    }

    if (totalCookies > 0) {
      const complianceRate = (compliantCookies / totalCookies) * 100;
      score = Math.round(complianceRate * 0.3); // 30% of total score for attributes

      evidence.push({
        type: 'cookie',
        description: 'Cookie attribute compliance',
        value: `${compliantCookies}/${totalCookies} cookies have proper attributes (${Math.round(complianceRate)}%)`,
      });
    }

    return { score, evidence };
  }

  /**
   * Save detailed cookie analysis to database
   */
  private async saveCookieAnalysisToDatabase(
    scanId: string,
    classification: {
      essential: Cookie[];
      analytics: Cookie[];
      marketing: Cookie[];
      functional: Cookie[];
      unclassified: Cookie[];
    },
    attributeAnalysis: any[]
  ): Promise<void> {
    try {
      const cookieRecords: Array<{
        name: string;
        domain: string;
        category: string;
        hasConsent: boolean;
        secureFlag: boolean;
        httpOnlyFlag: boolean;
        sameSiteAttribute?: string;
        expiryDate?: Date;
        purpose?: string;
        thirdParty: boolean;
      }> = [];

      // Process each category
      for (const [category, cookies] of Object.entries(classification)) {
        for (const cookie of cookies as Cookie[]) {
          const analysis = attributeAnalysis.find(
            (a) => a.cookieId === `${cookie.domain}-${cookie.name}`,
          );

          cookieRecords.push({
            name: cookie.name,
            domain: cookie.domain,
            category,
            hasConsent: category === 'essential', // Essential cookies don't need explicit consent
            secureFlag: cookie.secure,
            httpOnlyFlag: cookie.httpOnly,
            sameSiteAttribute: cookie.sameSite,
            expiryDate: cookie.expires,
            purpose: this.inferCookiePurpose(cookie, category),
            thirdParty: this.isThirdPartyCookie(cookie),
          });
        }
      }

      await GdprDatabase.saveCookieAnalysis(scanId, cookieRecords);
    } catch (error) {
      console.error('Failed to save cookie analysis:', error);
    }
  }

  /**
   * Infer cookie purpose based on name and category
   */
  private inferCookiePurpose(cookie: Cookie, category: string): string {
    const name = cookie.name.toLowerCase();

    if (category === 'essential') {
      if (name.includes('session') || name.includes('sess')) return 'Session management';
      if (name.includes('csrf') || name.includes('token')) return 'Security token';
      if (name.includes('auth') || name.includes('login')) return 'Authentication';
      if (name.includes('cart') || name.includes('basket')) return 'Shopping cart';
      return 'Essential functionality';
    }

    if (category === 'analytics') {
      if (name.includes('ga') || name.includes('utm')) return 'Google Analytics';
      if (name.includes('hj')) return 'Hotjar analytics';
      return 'Website analytics';
    }

    if (category === 'marketing') {
      if (name.includes('fb') || name.includes('facebook')) return 'Facebook advertising';
      if (name.includes('gads') || name.includes('doubleclick')) return 'Google advertising';
      return 'Marketing and advertising';
    }

    return 'Functional enhancement';
  }

  /**
   * Check if cookie is from third party
   */
  private isThirdPartyCookie(cookie: Cookie): boolean {
    const thirdPartyDomains = [
      'google.com', 'facebook.com', 'twitter.com', 'linkedin.com',
      'doubleclick.net', 'adsystem.amazon.com', 'hotjar.com',
    ];

    return thirdPartyDomains.some((domain) => cookie.domain.includes(domain));
  }

  /**
   * Generate cookie-specific recommendations
   */
  private generateCookieRecommendations(
    classification: {
      analytics: Cookie[];
      marketing: Cookie[];
      functional: Cookie[];
      unclassified: Cookie[];
    },
    score: number,
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const nonEssentialCount =
      classification.analytics.length +
      classification.marketing.length +
      classification.functional.length;

    if (nonEssentialCount > 0 && score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Implement consent-based cookie blocking',
        description: 'Block non-essential cookies until user provides consent',
        implementation:
          'Configure cookie management system to block analytics and marketing cookies before consent',
        effort: 'significant',
        impact: 'high',
      });
    }

    if (classification.unclassified.length > 0) {
      recommendations.push({
        priority: 2,
        title: 'Classify unidentified cookies',
        description: `${classification.unclassified.length} cookies need proper classification`,
        implementation: 'Review and categorize all cookies according to their purpose',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    return recommendations;
  }
}
