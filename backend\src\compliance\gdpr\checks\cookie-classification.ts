import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface CookieClassificationCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
}

export class CookieClassificationCheck {
  /**
   * Check cookie classification and consent-based blocking
   * REAL ANALYSIS - analyzes actual cookies and their behavior
   */
  async performCheck(config: CookieClassificationCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      // First visit - check cookies without consent
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Get initial cookies (before consent)
      const initialCookies = await page.cookies();
      
      // Analyze cookie categories
      const cookieAnalysis = this.analyzeCookieCategories(initialCookies);
      
      // Check for consent banner and try to accept
      const consentInteraction = await this.interactWithConsentBanner(page);
      
      // Get cookies after consent
      const postConsentCookies = await page.cookies();
      
      // Compare cookie behavior
      const behaviorAnalysis = this.analyzeCookieBehavior(
        initialCookies, 
        postConsentCookies, 
        cookieAnalysis
      );

      // Calculate compliance score
      score = this.calculateComplianceScore(cookieAnalysis, behaviorAnalysis, consentInteraction);

      // Generate evidence
      evidence.push({
        type: 'text',
        description: 'Initial cookies found',
        value: `${initialCookies.length} cookies detected before consent`,
      });

      evidence.push({
        type: 'text',
        description: 'Post-consent cookies',
        value: `${postConsentCookies.length} cookies detected after consent`,
      });

      // Add category analysis evidence
      for (const [category, cookies] of Object.entries(cookieAnalysis.categorizedCookies)) {
        if (cookies.length > 0) {
          evidence.push({
            type: 'text',
            description: `${category} cookies`,
            value: `${cookies.length} cookies: ${cookies.map(c => c.name).join(', ')}`,
          });
        }
      }

      // Add behavior analysis evidence
      if (behaviorAnalysis.nonEssentialBeforeConsent.length > 0) {
        evidence.push({
          type: 'text',
          description: 'Non-essential cookies set before consent',
          value: `${behaviorAnalysis.nonEssentialBeforeConsent.length} violations found`,
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed,
        score,
        weight: 8,
        severity: 'critical',
        evidence,
        recommendations: this.generateCookieRecommendations(cookieAnalysis, behaviorAnalysis),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed: false,
        score: 0,
        weight: 8,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Cookie classification check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement proper cookie management',
          description: 'Set up cookie categorization and consent-based blocking',
          implementation: 'Use a cookie management platform to categorize and control cookies',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze cookie categories based on name patterns and domains
   */
  private analyzeCookieCategories(cookies: any[]): {
    categorizedCookies: Record<string, any[]>;
    totalCookies: number;
  } {
    const categorizedCookies: Record<string, any[]> = {
      essential: [],
      analytics: [],
      marketing: [],
      functional: [],
      unknown: [],
    };

    const categoryPatterns = {
      essential: [
        /session/i, /csrf/i, /auth/i, /login/i, /security/i, /consent/i,
        /gdpr/i, /cookie.*consent/i, /necessary/i
      ],
      analytics: [
        /_ga/i, /_gid/i, /_gat/i, /gtag/i, /analytics/i, /tracking/i,
        /adobe/i, /omniture/i, /hotjar/i, /mixpanel/i
      ],
      marketing: [
        /facebook/i, /fb/i, /twitter/i, /linkedin/i, /doubleclick/i,
        /adsystem/i, /advertising/i, /marketing/i, /retargeting/i
      ],
      functional: [
        /preference/i, /settings/i, /language/i, /currency/i, /theme/i,
        /functional/i, /feature/i
      ],
    };

    for (const cookie of cookies) {
      let categorized = false;
      
      for (const [category, patterns] of Object.entries(categoryPatterns)) {
        if (patterns.some(pattern => 
          pattern.test(cookie.name) || pattern.test(cookie.domain)
        )) {
          categorizedCookies[category].push(cookie);
          categorized = true;
          break;
        }
      }
      
      if (!categorized) {
        categorizedCookies.unknown.push(cookie);
      }
    }

    return {
      categorizedCookies,
      totalCookies: cookies.length,
    };
  }

  /**
   * Try to interact with consent banner
   */
  private async interactWithConsentBanner(page: Page): Promise<{
    bannerFound: boolean;
    acceptClicked: boolean;
    rejectAvailable: boolean;
  }> {
    try {
      // Wait a bit for banner to appear
      await new Promise(resolve => setTimeout(resolve, 2000));

      const bannerInfo = await page.evaluate(() => {
        // Look for consent banners
        const bannerSelectors = [
          '[class*="cookie"]', '[class*="consent"]', '[class*="gdpr"]',
          '[id*="cookie"]', '[id*="consent"]'
        ];

        let bannerElement: Element | null = null;
        for (const selector of bannerSelectors) {
          const elements = document.querySelectorAll(selector);
          for (const element of elements) {
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              bannerElement = element;
              break;
            }
          }
          if (bannerElement) break;
        }

        if (!bannerElement) {
          return { bannerFound: false, acceptClicked: false, rejectAvailable: false };
        }

        // Look for accept button
        const buttons = bannerElement.querySelectorAll('button, a, input[type="button"]');
        let acceptButton: Element | null = null;
        let rejectButton: Element | null = null;

        for (const button of buttons) {
          const text = button.textContent?.toLowerCase() || '';
          if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
            acceptButton = button;
          }
          if (text.includes('reject') || text.includes('decline') || text.includes('deny')) {
            rejectButton = button;
          }
        }

        // Try to click accept button
        let acceptClicked = false;
        if (acceptButton) {
          (acceptButton as HTMLElement).click();
          acceptClicked = true;
        }

        return {
          bannerFound: true,
          acceptClicked,
          rejectAvailable: !!rejectButton,
        };
      });

      // Wait for potential page changes after consent
      if (bannerInfo.acceptClicked) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      return bannerInfo;
    } catch (error) {
      return { bannerFound: false, acceptClicked: false, rejectAvailable: false };
    }
  }

  /**
   * Analyze cookie behavior before and after consent
   */
  private analyzeCookieBehavior(
    initialCookies: any[],
    postConsentCookies: any[],
    cookieAnalysis: { categorizedCookies: Record<string, any[]> }
  ): {
    nonEssentialBeforeConsent: any[];
    newCookiesAfterConsent: any[];
    properBlocking: boolean;
  } {
    // Find non-essential cookies that were set before consent
    const nonEssentialBeforeConsent = initialCookies.filter(cookie => {
      return !cookieAnalysis.categorizedCookies.essential.some(essential => 
        essential.name === cookie.name
      );
    });

    // Find new cookies after consent
    const newCookiesAfterConsent = postConsentCookies.filter(postCookie => {
      return !initialCookies.some(initialCookie => 
        initialCookie.name === postCookie.name
      );
    });

    // Check if blocking is working properly
    const properBlocking = nonEssentialBeforeConsent.length === 0;

    return {
      nonEssentialBeforeConsent,
      newCookiesAfterConsent,
      properBlocking,
    };
  }

  /**
   * Calculate compliance score
   */
  private calculateComplianceScore(
    cookieAnalysis: any,
    behaviorAnalysis: any,
    consentInteraction: any
  ): number {
    let score = 0;

    // Proper categorization (30 points)
    const totalCookies = cookieAnalysis.totalCookies;
    const unknownCookies = cookieAnalysis.categorizedCookies.unknown.length;
    if (totalCookies > 0) {
      score += Math.round(((totalCookies - unknownCookies) / totalCookies) * 30);
    } else {
      score += 30; // No cookies is compliant
    }

    // Proper blocking (40 points)
    if (behaviorAnalysis.properBlocking) {
      score += 40;
    }

    // Consent mechanism (30 points)
    if (consentInteraction.bannerFound) {
      score += 15;
      if (consentInteraction.rejectAvailable) {
        score += 15;
      }
    }

    return Math.min(100, score);
  }

  /**
   * Generate cookie-specific recommendations
   */
  private generateCookieRecommendations(
    cookieAnalysis: any,
    behaviorAnalysis: any
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (behaviorAnalysis.nonEssentialBeforeConsent.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Block non-essential cookies before consent',
        description: `${behaviorAnalysis.nonEssentialBeforeConsent.length} non-essential cookies are set before user consent`,
        implementation: 'Implement cookie blocking mechanism that prevents non-essential cookies until consent is given',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (cookieAnalysis.categorizedCookies.unknown.length > 0) {
      recommendations.push({
        priority: 2,
        title: 'Categorize unknown cookies',
        description: `${cookieAnalysis.categorizedCookies.unknown.length} cookies need proper categorization`,
        implementation: 'Review and categorize all cookies as essential, analytics, marketing, or functional',
        effort: 'minimal',
        impact: 'medium',
      });
    }

    return recommendations;
  }
}
