import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface GpcDntCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class GpcDntCheck {
  /**
   * Check Global Privacy Control and Do-Not-Track support
   * REAL ANALYSIS - tests actual GPC/DNT signal handling
   */
  async performCheck(config: GpcDntCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      // Test with GPC enabled
      const pageWithGpc = await browser.newPage();
      await pageWithGpc.setExtraHTTPHeaders({ 'Sec-GPC': '1' });
      await pageWithGpc.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await pageWithGpc.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Check if GPC is detected and respected
      const gpcResponse = await pageWithGpc.evaluate(() => {
        return {
          gpcDetected: !!(navigator as any).globalPrivacyControl,
          cookieCount: document.cookie.split(';').filter(c => c.trim()).length,
          hasGpcScript: document.body.textContent?.toLowerCase().includes('globalprivacycontrol') || false,
        };
      });

      // Test without GPC for comparison
      const pageNoGpc = await browser.newPage();
      await pageNoGpc.setUserAgent('GDPR-Compliance-Scanner/1.0');
      await pageNoGpc.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      const normalResponse = await pageNoGpc.evaluate(() => {
        return {
          cookieCount: document.cookie.split(';').filter(c => c.trim()).length,
        };
      });

      // Analyze GPC support
      if (gpcResponse.gpcDetected) {
        score += 30;
        evidence.push({
          type: 'text',
          description: 'GPC signal detected in browser',
          value: 'navigator.globalPrivacyControl is available',
        });
      } else {
        evidence.push({
          type: 'text',
          description: 'GPC signal not detected',
          value: 'navigator.globalPrivacyControl not available',
        });
      }

      if (gpcResponse.hasGpcScript) {
        score += 20;
        evidence.push({
          type: 'text',
          description: 'GPC-related code found on page',
          value: 'Page contains GPC handling code',
        });
      }

      // Check if GPC affects cookie behavior
      if (gpcResponse.cookieCount < normalResponse.cookieCount) {
        score += 50;
        evidence.push({
          type: 'cookie',
          description: 'GPC respected - fewer cookies set',
          value: `${gpcResponse.cookieCount} vs ${normalResponse.cookieCount} cookies`,
        });
      } else if (gpcResponse.cookieCount === normalResponse.cookieCount && gpcResponse.cookieCount > 0) {
        evidence.push({
          type: 'cookie',
          description: 'GPC not respected - same cookie behavior',
          value: 'No difference in cookie setting behavior',
        });
      } else if (gpcResponse.cookieCount === 0 && normalResponse.cookieCount === 0) {
        score += 25; // Partial credit for no cookies at all
        evidence.push({
          type: 'cookie',
          description: 'No cookies set regardless of GPC',
          value: 'No privacy concerns with cookie setting',
        });
      }

      // Test DNT header support
      const pageWithDnt = await browser.newPage();
      await pageWithDnt.setExtraHTTPHeaders({ 'DNT': '1' });
      await pageWithDnt.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      const dntResponse = await pageWithDnt.evaluate(() => {
        return {
          dntDetected: (navigator as any).doNotTrack === '1',
          cookieCount: document.cookie.split(';').filter(c => c.trim()).length,
        };
      });

      if (dntResponse.cookieCount < normalResponse.cookieCount) {
        score += 25;
        evidence.push({
          type: 'cookie',
          description: 'DNT header respected',
          value: `Fewer cookies with DNT: ${dntResponse.cookieCount} vs ${normalResponse.cookieCount}`,
        });
      }

      const passed = score >= 50;

      return {
        ruleId: 'GDPR-008',
        ruleName: 'Global Privacy Control/Do-Not-Track',
        category: 'consent',
        passed,
        score,
        weight: 4,
        severity: 'medium',
        evidence,
        recommendations: this.generateRecommendations(score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Generate recommendations based on score
   */
  private generateRecommendations(score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 50) {
      recommendations.push({
        priority: 1,
        title: 'Implement GPC support',
        description: 'Honor Global Privacy Control signals from users',
        implementation: 'Add JavaScript to detect and respect GPC headers and navigator.globalPrivacyControl',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    if (score < 25) {
      recommendations.push({
        priority: 2,
        title: 'Add DNT header support',
        description: 'Respect Do-Not-Track headers from browsers',
        implementation: 'Configure server and scripts to honor DNT=1 headers',
        effort: 'minimal',
        impact: 'low',
      });
    }

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-008',
      ruleName: 'Global Privacy Control/Do-Not-Track',
      category: 'consent',
      passed: false,
      score: 0,
      weight: 4,
      severity: 'medium',
      evidence: [{
        type: 'text',
        description: 'GPC/DNT check failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }],
      recommendations: [{
        priority: 1,
        title: 'Implement GPC support',
        description: 'Add Global Privacy Control detection and handling',
        implementation: 'Configure GPC handling in privacy scripts',
        effort: 'moderate',
        impact: 'medium',
      }],
      manualReviewRequired: false,
    };
  }
}
