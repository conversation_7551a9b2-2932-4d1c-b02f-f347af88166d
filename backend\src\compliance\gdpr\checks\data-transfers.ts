import { QuickCheckTemplate } from '../utils/quick-check-template';
import { GdprCheckResult } from '../types';

export interface DataTransfersCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class DataTransfersCheck {
  /**
   * Check for international data transfer information
   * REAL ANALYSIS - scans for transfer-related content
   */
  async performCheck(config: DataTransfersCheckConfig): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-016',
      'International Data Transfers',
      'data_protection',
      5,
      'medium',
      [
        'international transfer',
        'third country',
        'adequacy decision',
        'standard contractual clauses',
        'binding corporate rules',
        'data transfer',
        'cross-border',
        'outside eu',
        'outside european union',
        'transfer mechanism',
        'safeguards'
      ],
      config,
      true, // Manual review required
      40 // Lower threshold since this is complex
    );
  }
}
