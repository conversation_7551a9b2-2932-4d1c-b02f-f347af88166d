/**
 * GDPR Compliance Checks - Index
 * 
 * Exports all individual GDPR compliance check classes.
 * Each check performs real website analysis - NO MOCK DATA.
 */

// Core Security Checks
export { HttpsTlsCheck } from './https-tls';
export { SecurityHeadersCheck } from './security-headers';

// Privacy Policy Checks
export { PrivacyPolicyCheck } from './privacy-policy';
export { PrivacyContentCheck } from './privacy-content';

// Consent and Cookie Checks
export { CookieConsentCheck } from './cookie-consent';
export { CookieClassificationCheck } from './cookie-classification';

// Third-Party and Tracking Checks
export { TrackerDetectionCheck } from './tracker-detection';

// Data Rights Checks
export { DataRightsCheck } from './data-rights';

// Data Protection Checks
export { IpAnonymizationCheck } from './ip-anonymization';

// Placeholder exports for checks to be implemented in later parts
// These will be implemented in Part 4 and beyond

// PrivacyContentCheck now implemented in ./privacy-content.ts

// CookieClassificationCheck now implemented in ./cookie-classification.ts

// TrackerDetectionCheck now implemented in ./tracker-detection.ts

// Cookie Attributes Check - implemented in separate file
export { CookieAttributesCheck } from './cookie-attributes';

// GPC/DNT and Form Consent Checks - implemented in separate files
export { GpcDntCheck } from './gpc-dnt';
export { FormConsentCheck } from './form-consent';

// IpAnonymizationCheck now implemented in ./ip-anonymization.ts

// Special Data and Children's Consent Checks - implemented in separate files
export { SpecialDataCheck } from './special-data';
export { ChildrenConsentCheck } from './children-consent';

// Organizational and Data Transfer Checks - implemented in separate files
export { DpoContactCheck } from './dpo-contact';
export { DataTransfersCheck } from './data-transfers';
export { BreachNotificationCheck } from './breach-notification';
export { DpiaCheck } from './dpia';
export { DataRetentionCheck } from './data-retention';
export { ProcessorAgreementsCheck } from './processor-agreements';
export { ImprintContactCheck } from './imprint-contact';

// Note: All 21 GDPR rules (GDPR-001 to GDPR-021) are now implemented
// No additional checks needed beyond the 21 defined rules
